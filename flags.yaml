new-dashboard:
  variations:
    enabled: true
    disabled: false
  defaultRule:
    variation: disabled
  targeting:
    - name: "beta-users"
      query: "beta == true"
      percentage:
        enabled: 100
        disabled: 0
    - name: "regular-users"
      query: "beta == false"
      percentage:
        enabled: 100
        disabled: 0

enhanced-logging:
  variations:
    on: true
    off: false
  defaultRule:
    variation: off
  targeting:
    - name: "admin-users"
      query: 'role == "admin"'
      percentage:
        on: 100
        off: 0

payment-method-v2:
  variations:
    v2: "payment-v2"
    v1: "payment-v1"
  defaultRule:
    variation: v1
  targeting:
    - name: "premium-users"
      query: 'plan == "premium"'
      percentage:
        v2: 80
        v1: 20
    - name: "basic-users"
      query: 'plan == "basic"'
      percentage:
        v2: 20
        v1: 80

api-rate-limit:
  variations:
    high: 1000
    medium: 500
    low: 100
  defaultRule:
    variation: medium
  targeting:
    - name: "premium-customers"
      query: 'plan == "premium"'
      percentage:
        high: 100
        medium: 0
        low: 0
    - name: "basic-customers"
      query: 'plan == "basic"'
      percentage:
        high: 0
        medium: 100
        low: 0
