package main

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	gofeatureflag "github.com/open-feature/go-sdk-contrib/providers/go-feature-flag-in-process/pkg"
	"github.com/open-feature/go-sdk/openfeature"
	ffclient "github.com/thomaspoignant/go-feature-flag"
	"github.com/thomaspoignant/go-feature-flag/retriever/fileretriever"
)

type User struct {
	ID   string `json:"id"`
	Role string `json:"role"`
	Plan string `json:"plan"`
	Beta bool   `json:"beta"`
}

type FeatureFlagService struct {
	client *openfeature.Client
}

func NewFeatureFlagService() *FeatureFlagService {
	// Create OpenFeature provider using go-feature-flag-in-process
	options := gofeatureflag.ProviderOptions{
		GOFeatureFlagConfig: &ffclient.Config{
			PollingInterval: 3 * time.Second,
			Context:         context.Background(),
			Retriever: &fileretriever.Retriever{
				Path: "flags.yaml",
			},
		},
	}
	provider, err := gofeatureflag.NewProvider(options)
	if err != nil {
		log.Fatal("Failed to create OpenFeature provider:", err)
	}

	// Set the provider
	openfeature.SetProvider(provider)

	// Create OpenFeature client
	client := openfeature.NewClient("feature-flag-demo")

	return &FeatureFlagService{
		client: client,
	}
}

func (ffs *FeatureFlagService) IsFeatureEnabled(ctx context.Context, flagKey string, user User) bool {
	// Create OpenFeature evaluation context
	evaluationContext := openfeature.NewEvaluationContext(
		"user-"+user.ID,
		map[string]interface{}{
			"role": user.Role,
			"plan": user.Plan,
			"beta": user.Beta,
		},
	)

	// Evaluate boolean flag using OpenFeature
	result, err := ffs.client.BooleanValue(ctx, flagKey, false, evaluationContext)
	if err != nil {
		log.Printf("Error evaluating flag %s: %v", flagKey, err)
		return false
	}

	return result
}

func (ffs *FeatureFlagService) GetStringFlag(ctx context.Context, flagKey string, defaultValue string, user User) string {
	// Create OpenFeature evaluation context
	evaluationContext := openfeature.NewEvaluationContext(
		"user-"+user.ID,
		map[string]interface{}{
			"role": user.Role,
			"plan": user.Plan,
			"beta": user.Beta,
		},
	)

	// Evaluate string flag using OpenFeature
	result, err := ffs.client.StringValue(ctx, flagKey, defaultValue, evaluationContext)
	if err != nil {
		log.Printf("Error evaluating flag %s: %v", flagKey, err)
		return defaultValue
	}

	return result
}

func (ffs *FeatureFlagService) GetIntFlag(ctx context.Context, flagKey string, defaultValue int, user User) int {
	// Create OpenFeature evaluation context
	evaluationContext := openfeature.NewEvaluationContext(
		"user-"+user.ID,
		map[string]interface{}{
			"role": user.Role,
			"plan": user.Plan,
			"beta": user.Beta,
		},
	)

	// Evaluate integer flag using OpenFeature
	result, err := ffs.client.IntValue(ctx, flagKey, int64(defaultValue), evaluationContext)
	if err != nil {
		log.Printf("Error evaluating flag %s: %v", flagKey, err)
		return defaultValue
	}

	return int(result)
}

func main() {
	e := echo.New()

	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	ffs := NewFeatureFlagService()

	e.GET("/", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{
			"message": "Feature Flag Demo API with OpenFeature",
			"version": "1.0.0",
		})
	})

	e.GET("/dashboard/:userId", func(c echo.Context) error {
		userId := c.Param("userId")
		role := c.QueryParam("role")
		plan := c.QueryParam("plan")
		beta := c.QueryParam("beta") == "true"

		user := User{
			ID:   userId,
			Role: role,
			Plan: plan,
			Beta: beta,
		}

		newDashboard := ffs.IsFeatureEnabled(c.Request().Context(), "new-dashboard", user)

		var dashboardVersion string
		if newDashboard {
			dashboardVersion = "v2"
		} else {
			dashboardVersion = "v1"
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"user":              user,
			"dashboard_version": dashboardVersion,
			"feature_flags": map[string]bool{
				"new-dashboard": newDashboard,
			},
		})
	})

	e.GET("/logging/:userId", func(c echo.Context) error {
		userId := c.Param("userId")
		role := c.QueryParam("role")
		plan := c.QueryParam("plan")
		beta := c.QueryParam("beta") == "true"

		user := User{
			ID:   userId,
			Role: role,
			Plan: plan,
			Beta: beta,
		}

		enhancedLogging := ffs.IsFeatureEnabled(c.Request().Context(), "enhanced-logging", user)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"user":             user,
			"enhanced_logging": enhancedLogging,
			"log_level":        map[bool]string{true: "DEBUG", false: "INFO"}[enhancedLogging],
		})
	})

	e.GET("/payment/:userId", func(c echo.Context) error {
		userId := c.Param("userId")
		role := c.QueryParam("role")
		plan := c.QueryParam("plan")
		beta := c.QueryParam("beta") == "true"

		user := User{
			ID:   userId,
			Role: role,
			Plan: plan,
			Beta: beta,
		}

		paymentMethod := ffs.GetStringFlag(c.Request().Context(), "payment-method-v2", "payment-v1", user)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"user":           user,
			"payment_method": paymentMethod,
			"features": map[string]interface{}{
				"one_click_payment": paymentMethod == "payment-v2",
				"saved_cards":       paymentMethod == "payment-v2",
			},
		})
	})

	e.GET("/rate-limit/:userId", func(c echo.Context) error {
		userId := c.Param("userId")
		role := c.QueryParam("role")
		plan := c.QueryParam("plan")
		beta := c.QueryParam("beta") == "true"

		user := User{
			ID:   userId,
			Role: role,
			Plan: plan,
			Beta: beta,
		}

		rateLimit := ffs.GetIntFlag(c.Request().Context(), "api-rate-limit", 100, user)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"user":                user,
			"rate_limit":          rateLimit,
			"requests_per_minute": rateLimit,
		})
	})

	e.GET("/flags/:userId", func(c echo.Context) error {
		userId := c.Param("userId")
		role := c.QueryParam("role")
		plan := c.QueryParam("plan")
		beta := c.QueryParam("beta") == "true"

		user := User{
			ID:   userId,
			Role: role,
			Plan: plan,
			Beta: beta,
		}

		flags := map[string]interface{}{
			"new-dashboard":     ffs.IsFeatureEnabled(c.Request().Context(), "new-dashboard", user),
			"enhanced-logging":  ffs.IsFeatureEnabled(c.Request().Context(), "enhanced-logging", user),
			"payment-method-v2": ffs.GetStringFlag(c.Request().Context(), "payment-method-v2", "payment-v1", user),
			"api-rate-limit":    ffs.GetIntFlag(c.Request().Context(), "api-rate-limit", 100, user),
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"user":  user,
			"flags": flags,
		})
	})

	log.Println("Server starting on :8080")
	e.Logger.Fatal(e.Start(":8080"))
}
